/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Represents feedback data for AI learning
 */
public class FeedbackData implements Parcelable {
    private final String mFeedbackId;
    private final String mFeedbackType;
    private final String mTargetId;
    private final Bundle mFeedbackData;
    private final float mRating;
    private final long mTimestamp;

    public FeedbackData(String feedbackType, String targetId, Bundle data, float rating) {
        mFeedbackId = generateFeedbackId();
        mFeedbackType = feedbackType;
        mTargetId = targetId;
        mFeedbackData = data != null ? new Bundle(data) : new Bundle();
        mRating = rating;
        mTimestamp = System.currentTimeMillis();
    }

    private FeedbackData(Parcel in) {
        mFeedbackId = in.readString();
        mFeedbackType = in.readString();
        mTargetId = in.readString();
        mFeedbackData = in.readBundle(getClass().getClassLoader());
        mRating = in.readFloat();
        mTimestamp = in.readLong();
    }

    public String getFeedbackId() {
        return mFeedbackId;
    }

    public String getFeedbackType() {
        return mFeedbackType;
    }

    public String getTargetId() {
        return mTargetId;
    }

    public Bundle getFeedbackData() {
        return new Bundle(mFeedbackData);
    }

    public float getRating() {
        return mRating;
    }

    public long getTimestamp() {
        return mTimestamp;
    }

    private String generateFeedbackId() {
        return "feedback_" + System.currentTimeMillis() + "_" + hashCode();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mFeedbackId);
        dest.writeString(mFeedbackType);
        dest.writeString(mTargetId);
        dest.writeBundle(mFeedbackData);
        dest.writeFloat(mRating);
        dest.writeLong(mTimestamp);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<FeedbackData> CREATOR = new Creator<FeedbackData>() {
        @Override
        public FeedbackData createFromParcel(Parcel in) {
            return new FeedbackData(in);
        }

        @Override
        public FeedbackData[] newArray(int size) {
            return new FeedbackData[size];
        }
    };
}

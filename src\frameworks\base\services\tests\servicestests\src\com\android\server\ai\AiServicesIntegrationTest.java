/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;

import androidx.test.InstrumentationRegistry;
import androidx.test.runner.AndroidJUnit4;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Integration tests for AI Services in Jarvis OS
 * 
 * Tests the interaction and coordination between all AI services
 * to ensure proper system-wide AI functionality.
 */
@RunWith(AndroidJUnit4.class)
public class AiServicesIntegrationTest {
    private static final String TAG = "AiServicesIntegrationTest";
    private static final long TEST_TIMEOUT_MS = 5000;

    private Context mContext;
    private HandlerThread mTestThread;
    private Handler mTestHandler;
    
    // AI Services under test
    private AiContextEngineService mContextEngineService;
    private AiPersonalizationService mPersonalizationService;
    private AiPlanningOrchestrationService mPlanningOrchestrationService;
    private AiUserInterfaceService mUserInterfaceService;
    private AiServiceCoordinator mServiceCoordinator;

    @Mock
    private Context mMockContext;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        
        mContext = InstrumentationRegistry.getTargetContext();
        
        // Initialize test thread
        mTestThread = new HandlerThread("AiServicesIntegrationTest");
        mTestThread.start();
        mTestHandler = new Handler(mTestThread.getLooper());
        
        // Initialize AI services
        initializeAiServices();
        
        // Wait for services to initialize
        Thread.sleep(1000);
    }

    @After
    public void tearDown() throws Exception {
        if (mTestThread != null) {
            mTestThread.quitSafely();
            mTestThread.join(1000);
        }
    }

    private void initializeAiServices() {
        // Initialize services in dependency order
        mContextEngineService = new AiContextEngineService(mContext);
        mPersonalizationService = new AiPersonalizationService(mContext);
        mPlanningOrchestrationService = new AiPlanningOrchestrationService(mContext);
        mUserInterfaceService = new AiUserInterfaceService(mContext);
        mServiceCoordinator = new AiServiceCoordinator(mContext);
        
        // Start services
        mContextEngineService.onStart();
        mPersonalizationService.onStart();
        mPlanningOrchestrationService.onStart();
        mUserInterfaceService.onStart();
        mServiceCoordinator.onStart();
        
        // Complete boot phases
        int[] bootPhases = {
            AiContextEngineService.PHASE_SYSTEM_SERVICES_READY,
            AiContextEngineService.PHASE_BOOT_COMPLETED
        };
        
        for (int phase : bootPhases) {
            mContextEngineService.onBootPhase(phase);
            mPersonalizationService.onBootPhase(phase);
            mPlanningOrchestrationService.onBootPhase(phase);
            mUserInterfaceService.onBootPhase(phase);
            mServiceCoordinator.onBootPhase(phase);
        }
    }

    @Test
    public void testContextEngineBasicFunctionality() throws Exception {
        // Test context engine basic operations
        Bundle contextData = new Bundle();
        contextData.putString("event_type", "app_launch");
        contextData.putString("package_name", "com.example.test");
        contextData.putLong("timestamp", System.currentTimeMillis());
        
        // Update context
        mContextEngineService.updateContext("test_source", contextData);
        
        // Wait for processing
        Thread.sleep(500);
        
        // Get insights
        List<Bundle> insights = mContextEngineService.getCurrentInsights();
        assertNotNull("Insights should not be null", insights);
        
        // Get statistics
        Bundle stats = mContextEngineService.getEngineStatistics();
        assertNotNull("Statistics should not be null", stats);
        assertTrue("Should have processed at least one context update", 
            stats.getInt("active_listeners", 0) >= 0);
    }

    @Test
    public void testPersonalizationServiceBasicFunctionality() throws Exception {
        // Test personalization service basic operations
        Bundle behaviorData = new Bundle();
        behaviorData.putString("action", "button_click");
        behaviorData.putString("ui_element", "settings_button");
        behaviorData.putLong("timestamp", System.currentTimeMillis());
        
        // Record behavior event
        mPersonalizationService.recordBehaviorEvent("ui_interactions", behaviorData);
        
        // Wait for processing
        Thread.sleep(500);
        
        // Get recommendations
        List<Bundle> recommendations = mPersonalizationService.getPersonalizedRecommendations("ui_interactions", 5);
        assertNotNull("Recommendations should not be null", recommendations);
        
        // Get statistics
        Bundle stats = mPersonalizationService.getStatistics();
        assertNotNull("Statistics should not be null", stats);
    }

    @Test
    public void testPlanningOrchestrationBasicFunctionality() throws Exception {
        // Test planning orchestration basic operations
        Bundle serviceInfo = new Bundle();
        serviceInfo.putString("service_type", "test_service");
        serviceInfo.putStringArray("supported_tasks", new String[]{"test_task"});
        
        // Register service
        boolean registered = mPlanningOrchestrationService.registerAiService("test_service", serviceInfo);
        assertTrue("Service registration should succeed", registered);
        
        // Submit task
        Bundle taskData = new Bundle();
        taskData.putString("task_type", "test_task");
        taskData.putInt("priority", 1);
        
        String taskId = mPlanningOrchestrationService.submitTask(taskData);
        assertNotNull("Task ID should not be null", taskId);
        
        // Get statistics
        Bundle stats = mPlanningOrchestrationService.getStatistics();
        assertNotNull("Statistics should not be null", stats);
    }

    @Test
    public void testUserInterfaceServiceBasicFunctionality() throws Exception {
        // Test UI service basic operations
        Bundle interactionData = new Bundle();
        interactionData.putString("ui_element", "button");
        interactionData.putString("action", "click");
        interactionData.putLong("timestamp", System.currentTimeMillis());
        
        // Record UI interaction
        mUserInterfaceService.recordUiInteraction("button_click", interactionData);
        
        // Wait for processing
        Thread.sleep(500);
        
        // Get UI adaptations
        Bundle contextData = new Bundle();
        contextData.putString("screen", "main");
        List<Bundle> adaptations = mUserInterfaceService.getUiAdaptations("layout", contextData);
        assertNotNull("Adaptations should not be null", adaptations);
        
        // Get statistics
        Bundle stats = mUserInterfaceService.getStatistics();
        assertNotNull("Statistics should not be null", stats);
    }

    @Test
    public void testServiceCoordinatorBasicFunctionality() throws Exception {
        // Test service coordinator basic operations
        List<String> serviceIds = new ArrayList<>();
        serviceIds.add("context_engine");
        serviceIds.add("personalization");
        
        Bundle coordinationData = new Bundle();
        coordinationData.putString("operation", "test_coordination");
        
        // Coordinate services
        String coordinationId = mServiceCoordinator.coordinateServices(
            serviceIds, "parallel", coordinationData);
        assertNotNull("Coordination ID should not be null", coordinationId);
        
        // Wait for coordination
        Thread.sleep(1000);
        
        // Get coordination status
        Bundle status = mServiceCoordinator.getCoordinationStatus(coordinationId);
        assertNotNull("Coordination status should not be null", status);
        
        // Get statistics
        Bundle stats = mServiceCoordinator.getStatistics();
        assertNotNull("Statistics should not be null", stats);
    }

    @Test
    public void testCrossServiceIntegration() throws Exception {
        final CountDownLatch latch = new CountDownLatch(1);
        final Bundle[] results = new Bundle[4];
        
        mTestHandler.post(() -> {
            try {
                // Test cross-service data flow
                
                // 1. Context Engine -> Personalization
                Bundle contextData = new Bundle();
                contextData.putString("event_type", "app_usage");
                contextData.putString("package_name", "com.example.app");
                mContextEngineService.updateContext("integration_test", contextData);
                
                Thread.sleep(200);
                
                // 2. Personalization -> UI Service
                Bundle behaviorData = new Bundle();
                behaviorData.putString("app_preference", "com.example.app");
                mPersonalizationService.recordBehaviorEvent("app_usage", behaviorData);
                
                Thread.sleep(200);
                
                // 3. UI Service -> Planning Orchestration
                Bundle uiData = new Bundle();
                uiData.putString("ui_optimization", "app_layout");
                mUserInterfaceService.recordUiInteraction("optimization_request", uiData);
                
                Thread.sleep(200);
                
                // 4. Collect results from all services
                results[0] = mContextEngineService.getEngineStatistics();
                results[1] = mPersonalizationService.getStatistics();
                results[2] = mUserInterfaceService.getStatistics();
                results[3] = mServiceCoordinator.getStatistics();
                
                latch.countDown();
                
            } catch (Exception e) {
                e.printStackTrace();
                latch.countDown();
            }
        });
        
        assertTrue("Cross-service integration test should complete", 
            latch.await(TEST_TIMEOUT_MS, TimeUnit.MILLISECONDS));
        
        // Verify all services processed data
        for (int i = 0; i < results.length; i++) {
            assertNotNull("Service " + i + " should have statistics", results[i]);
        }
    }

    @Test
    public void testSystemWideAiOperation() throws Exception {
        // Test system-wide AI operation coordination
        Bundle parameters = new Bundle();
        parameters.putString("operation_type", "full_analysis");
        parameters.putLong("timestamp", System.currentTimeMillis());
        
        // Perform context analysis
        Bundle contextResult = mServiceCoordinator.performSystemWideOperation("context_analysis", parameters);
        assertNotNull("Context analysis result should not be null", contextResult);
        assertTrue("Context analysis should succeed", contextResult.getBoolean("success", false));
        
        // Perform user personalization
        Bundle personalizationResult = mServiceCoordinator.performSystemWideOperation("user_personalization", parameters);
        assertNotNull("Personalization result should not be null", personalizationResult);
        assertTrue("Personalization should succeed", personalizationResult.getBoolean("success", false));
        
        // Perform UI optimization
        Bundle uiResult = mServiceCoordinator.performSystemWideOperation("ui_optimization", parameters);
        assertNotNull("UI optimization result should not be null", uiResult);
        assertTrue("UI optimization should succeed", uiResult.getBoolean("success", false));
    }

    @Test
    public void testServiceHealthMonitoring() throws Exception {
        // Test service health monitoring
        Bundle allStatuses = mServiceCoordinator.getAllServiceStatuses();
        assertNotNull("Service statuses should not be null", allStatuses);
        
        // Check that all expected services are present
        String[] expectedServices = {"context_engine", "personalization", "planning_orchestration", "user_interface"};
        
        for (String serviceId : expectedServices) {
            Bundle serviceStatus = allStatuses.getBundle(serviceId);
            assertNotNull("Service " + serviceId + " should have status", serviceStatus);
        }
    }

    @Test
    public void testPerformanceUnderLoad() throws Exception {
        final int NUM_OPERATIONS = 50;
        final CountDownLatch latch = new CountDownLatch(NUM_OPERATIONS);
        final long startTime = System.currentTimeMillis();
        
        // Submit multiple operations concurrently
        for (int i = 0; i < NUM_OPERATIONS; i++) {
            final int operationId = i;
            
            mTestHandler.post(() -> {
                try {
                    // Context update
                    Bundle contextData = new Bundle();
                    contextData.putString("event_type", "load_test_" + operationId);
                    mContextEngineService.updateContext("load_test", contextData);
                    
                    // Personalization event
                    Bundle behaviorData = new Bundle();
                    behaviorData.putString("action", "load_test_action_" + operationId);
                    mPersonalizationService.recordBehaviorEvent("load_test", behaviorData);
                    
                    // UI interaction
                    Bundle uiData = new Bundle();
                    uiData.putString("ui_element", "load_test_element_" + operationId);
                    mUserInterfaceService.recordUiInteraction("load_test", uiData);
                    
                    latch.countDown();
                    
                } catch (Exception e) {
                    e.printStackTrace();
                    latch.countDown();
                }
            });
        }
        
        assertTrue("Load test should complete within timeout", 
            latch.await(TEST_TIMEOUT_MS * 2, TimeUnit.MILLISECONDS));
        
        long totalTime = System.currentTimeMillis() - startTime;
        double operationsPerSecond = (double) NUM_OPERATIONS / (totalTime / 1000.0);
        
        assertTrue("Should handle at least 10 operations per second", operationsPerSecond >= 10.0);
        
        // Verify services are still responsive
        Bundle contextStats = mContextEngineService.getEngineStatistics();
        Bundle personalizationStats = mPersonalizationService.getStatistics();
        Bundle uiStats = mUserInterfaceService.getStatistics();
        Bundle coordinatorStats = mServiceCoordinator.getStatistics();
        
        assertNotNull("Context engine should still be responsive", contextStats);
        assertNotNull("Personalization service should still be responsive", personalizationStats);
        assertNotNull("UI service should still be responsive", uiStats);
        assertNotNull("Service coordinator should still be responsive", coordinatorStats);
    }

    @Test
    public void testErrorHandlingAndRecovery() throws Exception {
        // Test error handling in service coordination
        List<String> invalidServiceIds = new ArrayList<>();
        invalidServiceIds.add("non_existent_service");
        
        Bundle coordinationData = new Bundle();
        coordinationData.putString("operation", "error_test");
        
        // This should handle the error gracefully
        String coordinationId = mServiceCoordinator.coordinateServices(
            invalidServiceIds, "sequential", coordinationData);
        
        // Coordination should still return an ID but fail gracefully
        if (coordinationId != null) {
            Thread.sleep(1000);
            Bundle status = mServiceCoordinator.getCoordinationStatus(coordinationId);
            assertNotNull("Status should be available even for failed coordination", status);
        }
        
        // Services should still be functional after error
        Bundle stats = mServiceCoordinator.getStatistics();
        assertNotNull("Coordinator should still provide statistics after error", stats);
    }

    @Test
    public void testDataPrivacyAndSecurity() throws Exception {
        // Test that sensitive data is handled properly
        Bundle sensitiveData = new Bundle();
        sensitiveData.putString("user_id", "test_user_123");
        sensitiveData.putString("personal_info", "sensitive_data");
        sensitiveData.putBoolean("privacy_sensitive", true);
        
        // Record sensitive interaction
        mPersonalizationService.recordBehaviorEvent("privacy_test", sensitiveData);
        
        // Wait for processing
        Thread.sleep(500);
        
        // Verify service still functions but handles data appropriately
        Bundle stats = mPersonalizationService.getStatistics();
        assertNotNull("Service should still provide statistics", stats);
        
        // The actual privacy handling would be tested in the security manager
        // This test ensures the service doesn't crash with sensitive data
    }

    @Test
    public void testServiceStatisticsAccuracy() throws Exception {
        // Get initial statistics
        Bundle initialContextStats = mContextEngineService.getEngineStatistics();
        Bundle initialPersonalizationStats = mPersonalizationService.getStatistics();
        Bundle initialUiStats = mUserInterfaceService.getStatistics();
        Bundle initialCoordinatorStats = mServiceCoordinator.getStatistics();
        
        // Perform known operations
        int numOperations = 5;
        
        for (int i = 0; i < numOperations; i++) {
            Bundle data = new Bundle();
            data.putString("test_operation", "stats_test_" + i);
            
            mContextEngineService.updateContext("stats_test", data);
            mPersonalizationService.recordBehaviorEvent("stats_test", data);
            mUserInterfaceService.recordUiInteraction("stats_test", data);
        }
        
        // Wait for processing
        Thread.sleep(1000);
        
        // Get final statistics
        Bundle finalContextStats = mContextEngineService.getEngineStatistics();
        Bundle finalPersonalizationStats = mPersonalizationService.getStatistics();
        Bundle finalUiStats = mUserInterfaceService.getStatistics();
        Bundle finalCoordinatorStats = mServiceCoordinator.getStatistics();
        
        // Verify statistics were updated (exact counts may vary due to async processing)
        assertNotNull("Final context stats should not be null", finalContextStats);
        assertNotNull("Final personalization stats should not be null", finalPersonalizationStats);
        assertNotNull("Final UI stats should not be null", finalUiStats);
        assertNotNull("Final coordinator stats should not be null", finalCoordinatorStats);
    }
}

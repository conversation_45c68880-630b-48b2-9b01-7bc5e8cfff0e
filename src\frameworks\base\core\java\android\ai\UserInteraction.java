/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Represents a user interaction for AI learning
 */
public class UserInteraction implements Parcelable {
    private final String mInteractionId;
    private final String mInteractionType;
    private final String mPackageName;
    private final Bundle mInteractionData;
    private final long mTimestamp;

    public UserInteraction(String interactionType, String packageName, Bundle interactionData) {
        mInteractionId = generateInteractionId();
        mInteractionType = interactionType;
        mPackageName = packageName;
        mInteractionData = interactionData != null ? new Bundle(interactionData) : new Bundle();
        mTimestamp = System.currentTimeMillis();
    }

    private UserInteraction(Parcel in) {
        mInteractionId = in.readString();
        mInteractionType = in.readString();
        mPackageName = in.readString();
        mInteractionData = in.readBundle(getClass().getClassLoader());
        mTimestamp = in.readLong();
    }

    public String getInteractionId() {
        return mInteractionId;
    }

    public String getInteractionType() {
        return mInteractionType;
    }

    public String getPackageName() {
        return mPackageName;
    }

    public Bundle getInteractionData() {
        return new Bundle(mInteractionData);
    }

    public long getTimestamp() {
        return mTimestamp;
    }

    private String generateInteractionId() {
        return "interaction_" + System.currentTimeMillis() + "_" + hashCode();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mInteractionId);
        dest.writeString(mInteractionType);
        dest.writeString(mPackageName);
        dest.writeBundle(mInteractionData);
        dest.writeLong(mTimestamp);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<UserInteraction> CREATOR = new Creator<UserInteraction>() {
        @Override
        public UserInteraction createFromParcel(Parcel in) {
            return new UserInteraction(in);
        }

        @Override
        public UserInteraction[] newArray(int size) {
            return new UserInteraction[size];
        }
    };
}

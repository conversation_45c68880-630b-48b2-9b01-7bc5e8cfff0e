/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.os;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * A mapping from String keys to various Parcelable values.
 * This is a stub implementation for the Jarvis OS project.
 */
public final class Bundle implements Parcelable {
    private Map<String, Object> mMap;
    
    public Bundle() {
        mMap = new HashMap<>();
    }
    
    public Bundle(Bundle b) {
        if (b != null) {
            mMap = new HashMap<>(b.mMap);
        } else {
            mMap = new HashMap<>();
        }
    }
    
    public Bundle(int capacity) {
        mMap = new HashMap<>(capacity);
    }
    
    // String operations
    public void putString(String key, String value) {
        mMap.put(key, value);
    }
    
    public String getString(String key) {
        return (String) mMap.get(key);
    }
    
    public String getString(String key, String defaultValue) {
        String value = getString(key);
        return value != null ? value : defaultValue;
    }
    
    // Integer operations
    public void putInt(String key, int value) {
        mMap.put(key, value);
    }
    
    public int getInt(String key) {
        Integer value = (Integer) mMap.get(key);
        return value != null ? value : 0;
    }
    
    public int getInt(String key, int defaultValue) {
        Integer value = (Integer) mMap.get(key);
        return value != null ? value : defaultValue;
    }
    
    // Long operations
    public void putLong(String key, long value) {
        mMap.put(key, value);
    }
    
    public long getLong(String key) {
        Long value = (Long) mMap.get(key);
        return value != null ? value : 0L;
    }
    
    public long getLong(String key, long defaultValue) {
        Long value = (Long) mMap.get(key);
        return value != null ? value : defaultValue;
    }
    
    // Boolean operations
    public void putBoolean(String key, boolean value) {
        mMap.put(key, value);
    }
    
    public boolean getBoolean(String key) {
        Boolean value = (Boolean) mMap.get(key);
        return value != null ? value : false;
    }
    
    public boolean getBoolean(String key, boolean defaultValue) {
        Boolean value = (Boolean) mMap.get(key);
        return value != null ? value : defaultValue;
    }
    
    // Float operations
    public void putFloat(String key, float value) {
        mMap.put(key, value);
    }
    
    public float getFloat(String key) {
        Float value = (Float) mMap.get(key);
        return value != null ? value : 0.0f;
    }
    
    public float getFloat(String key, float defaultValue) {
        Float value = (Float) mMap.get(key);
        return value != null ? value : defaultValue;
    }
    
    // Double operations
    public void putDouble(String key, double value) {
        mMap.put(key, value);
    }
    
    public double getDouble(String key) {
        Double value = (Double) mMap.get(key);
        return value != null ? value : 0.0;
    }
    
    public double getDouble(String key, double defaultValue) {
        Double value = (Double) mMap.get(key);
        return value != null ? value : defaultValue;
    }
    
    // Bundle operations
    public void putBundle(String key, Bundle value) {
        mMap.put(key, value);
    }
    
    public Bundle getBundle(String key) {
        return (Bundle) mMap.get(key);
    }
    
    // String array operations
    public void putStringArray(String key, String[] value) {
        mMap.put(key, value);
    }
    
    public String[] getStringArray(String key) {
        return (String[]) mMap.get(key);
    }
    
    // Utility methods
    public boolean containsKey(String key) {
        return mMap.containsKey(key);
    }
    
    public Object get(String key) {
        return mMap.get(key);
    }
    
    public void remove(String key) {
        mMap.remove(key);
    }
    
    public void clear() {
        mMap.clear();
    }
    
    public Set<String> keySet() {
        return mMap.keySet();
    }
    
    public int size() {
        return mMap.size();
    }
    
    public boolean isEmpty() {
        return mMap.isEmpty();
    }
    
    // Parcelable implementation
    @Override
    public int describeContents() {
        return 0;
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        // Simplified implementation
        dest.writeInt(mMap.size());
        for (Map.Entry<String, Object> entry : mMap.entrySet()) {
            dest.writeString(entry.getKey());
            dest.writeValue(entry.getValue());
        }
    }
    
    public static final Creator<Bundle> CREATOR = new Creator<Bundle>() {
        @Override
        public Bundle createFromParcel(Parcel in) {
            Bundle bundle = new Bundle();
            int size = in.readInt();
            for (int i = 0; i < size; i++) {
                String key = in.readString();
                Object value = in.readValue(Bundle.class.getClassLoader());
                bundle.mMap.put(key, value);
            }
            return bundle;
        }
        
        @Override
        public Bundle[] newArray(int size) {
            return new Bundle[size];
        }
    };
    
    @Override
    public String toString() {
        return "Bundle{" + mMap + "}";
    }
}

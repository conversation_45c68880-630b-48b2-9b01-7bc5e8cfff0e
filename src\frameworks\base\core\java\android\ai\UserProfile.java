/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Represents a user profile for AI personalization
 */
public class UserProfile implements Parcelable {
    private final String mUserId;
    private final Bundle mPreferences;
    private final long mCreatedTime;
    private final long mLastUpdated;

    public UserProfile(String userId, Bundle preferences) {
        mUserId = userId;
        mPreferences = preferences != null ? new Bundle(preferences) : new Bundle();
        mCreatedTime = System.currentTimeMillis();
        mLastUpdated = mCreatedTime;
    }

    private UserProfile(Parcel in) {
        mUserId = in.readString();
        mPreferences = in.readBundle(getClass().getClassLoader());
        mCreatedTime = in.readLong();
        mLastUpdated = in.readLong();
    }

    public String getUserId() {
        return mUserId;
    }

    public Bundle getPreferences() {
        return new Bundle(mPreferences);
    }

    public long getCreatedTime() {
        return mCreatedTime;
    }

    public long getLastUpdated() {
        return mLastUpdated;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mUserId);
        dest.writeBundle(mPreferences);
        dest.writeLong(mCreatedTime);
        dest.writeLong(mLastUpdated);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<UserProfile> CREATOR = new Creator<UserProfile>() {
        @Override
        public UserProfile createFromParcel(Parcel in) {
            return new UserProfile(in);
        }

        @Override
        public UserProfile[] newArray(int size) {
            return new UserProfile[size];
        }
    };
}
